# 智能对话系统 API 文档

基于 LlamaIndex 和 Qdrant 的检索增强生成（RAG）对话系统，提供智能文档问答和对话管理功能。

## 🎯 系统概述

RAG 对话系统通过以下步骤实现智能问答：

1. **文档索引**: 将文档分块并转换为向量存储在 Qdrant 中
2. **智能检索**: 根据用户问题检索相关文档片段
3. **增强生成**: 结合检索内容和 LLM 生成准确回答
4. **对话记忆**: 使用 Redis 维护多轮对话上下文

## 🔧 技术架构

- **向量数据库**: Qdrant 1.15.1
- **文档处理**: LlamaIndex 0.13.0
- **嵌入模型**: text-embedding-3-small
- **语言模型**: gpt-4o-mini
- **对话存储**: Redis 5.0+

## 📋 API 端点概览

### 智能对话

| 端点                                             | 方法   | 功能         | 描述               |
| ------------------------------------------------ | ------ | ------------ | ------------------ |
| `/api/v1/conversation/chat`                      | POST   | 智能问答     | 基于文档的智能对话 |
| `/api/v1/conversation/conversations/{id}`        | DELETE | 清除会话     | 删除对话历史       |
| `/api/v1/conversation/conversations/{id}/status` | GET    | 获取会话状态 | 查看会话信息       |
| `/api/v1/conversation/config`                    | GET    | 获取配置信息 | 查看系统配置       |

## 🚀 核心功能详解

### 1. 智能问答对话

#### 基本用法

```bash
curl -X POST "http://localhost:8000/api/v1/conversation/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "user_session_123",
    "question": "什么是Python变量？",
    "course_id": "CS101",
    "chat_engine_type": "condense_plus_context"
  }'
```

#### 请求参数

| 参数                 | 类型   | 必需 | 描述               |
| -------------------- | ------ | ---- | ------------------ |
| `conversation_id`    | String | 是   | 会话标识符         |
| `question`           | String | 是   | 用户问题           |
| `chat_engine_type`   | String | 是   | 引擎类型（见下表） |
| `course_id`          | String | 否   | 课程过滤条件       |
| `course_material_id` | String | 否   | 材料过滤条件       |
| `collection_name`    | String | 否   | 集合名称           |

#### 聊天引擎类型

| 类型                    | 名称         | 描述                   | 适用场景           |
| ----------------------- | ------------ | ---------------------- | ------------------ |
| `condense_plus_context` | 检索增强模式 | 基于文档内容的智能问答 | 知识查询、专业问答 |
| `simple`                | 直接对话模式 | 不检索文档的自由对话   | 一般聊天、创意讨论 |

#### 响应示例

```json
{
  "answer": "Python中的变量是用来存储数据的容器。变量可以存储不同类型的数据，如数字、字符串、列表等。在Python中，变量不需要声明类型，可以直接赋值使用。",
  "sources": [
    {
      "content": "变量是Python编程的基础概念，用于存储和操作数据...",
      "metadata": {
        "course_id": "CS101",
        "course_material_id": "lesson01",
        "file_path": "data/uploads/CS101/lesson01_python_basics.md",
        "chunk_id": "chunk_001"
      },
      "score": 0.92
    }
  ],
  "conversation_id": "user_session_123",
  "chat_engine_type": "condense_plus_context",
  "filter_info": "过滤条件: course_id=CS101",
  "processing_time": 2.3,
  "token_usage": {
    "prompt_tokens": 1200,
    "completion_tokens": 150,
    "total_tokens": 1350
  }
}
```

### 2. 清除会话

#### 基本用法

```bash
curl -X DELETE "http://localhost:8000/api/v1/conversation/conversations/user_session_123"
```

#### 响应示例

```json
{
  "success": true,
  "message": "会话已成功删除",
  "conversation_id": "user_session_123"
}
```

### 3. 获取会话状态

#### 基本用法

```bash
curl -X GET "http://localhost:8000/api/v1/conversation/conversations/user_session_123/status"
```

#### 响应示例

```json
{
  "conversation_id": "user_session_123",
  "status": "active",
  "created_at": "2024-01-01T10:00:00Z",
  "last_activity": "2024-01-01T10:30:00Z",
  "message_count": 5,
  "ttl_remaining": 3200
}
```

### 4. 获取配置信息

#### 基本用法

```bash
curl -X GET "http://localhost:8000/api/v1/conversation/config"
```

#### 响应示例

```json
{
  "conversation_config": {
    "token_limit": 4000,
    "similarity_top_k": 6,
    "llm_temperature": 0.1,
    "redis_ttl": 3600
  },
  "available_engines": [
    "condense_plus_context",
    "simple"
  ],
  "models": {
    "llm_model": "gpt-4o-mini",
    "embed_model": "text-embedding-3-small"
  }
}
```

## 🔧 配置参数

### 对话系统配置

| 参数                            | 默认值                   | 描述                     |
| ------------------------------- | ------------------------ | ------------------------ |
| `REDIS_URL`                     | `redis://localhost:6379` | Redis 连接地址           |
| `REDIS_TTL`                     | `3600`                   | Redis 数据过期时间（秒） |
| `CONVERSATION_TOKEN_LIMIT`      | `4000`                   | 对话记忆 Token 限制      |
| `CONVERSATION_SIMILARITY_TOP_K` | `6`                      | 检索相似文档数量         |
| `LLM_TEMPERATURE`               | `0.1`                    | 模型温度参数             |
| `LLM_MODEL`                     | `gpt-4o-mini`            | 语言模型名称             |
| `EMBED_MODEL`                   | `text-embedding-3-small` | 嵌入模型名称             |

## 🧪 测试示例

### JavaScript 客户端

```javascript
class ConversationClient {
  constructor(baseUrl = "http://localhost:8000") {
    this.baseUrl = baseUrl;
  }

  // 智能问答
  async chat(conversationId, question, options = {}) {
    const requestBody = {
      conversation_id: conversationId,
      question: question,
      chat_engine_type: options.engineType || "condense_plus_context",
      ...options,
    };

    const response = await fetch(`${this.baseUrl}/api/v1/conversation/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    return await response.json();
  }

  // 清除会话
  async deleteConversation(conversationId) {
    const response = await fetch(`${this.baseUrl}/api/v1/conversation/conversations/${conversationId}`, {
      method: "DELETE",
    });

    return await response.json();
  }

  // 获取会话状态
  async getConversationStatus(conversationId) {
    const response = await fetch(`${this.baseUrl}/api/v1/conversation/conversations/${conversationId}/status`);
    return await response.json();
  }

  // 获取配置信息
  async getConfig() {
    const response = await fetch(`${this.baseUrl}/api/v1/conversation/config`);
    return await response.json();
  }
}

// 使用示例
const client = new ConversationClient();

// 1. 开始对话
const chatResult = await client.chat("user_session_123", "什么是Python变量？", {
  course_id: "CS101",
});
console.log("AI回答:", chatResult.answer);

// 2. 获取会话状态
const status = await client.getConversationStatus("user_session_123");
console.log("会话状态:", status);

// 3. 获取系统配置
const config = await client.getConfig();
console.log("系统配置:", config);

// 4. 清除会话
const deleteResult = await client.deleteConversation("user_session_123");
console.log("清除结果:", deleteResult);
```

## 🔍 故障排除

### 常见问题

1. **Redis 连接失败**

   ```
   Error: Redis connection failed
   ```

   - 检查 Redis 服务状态
   - 验证 `REDIS_URL` 配置

2. **对话上下文丢失**
   ```
   Error: Conversation not found
   ```
   - 检查 `conversation_id` 是否正确
   - 验证会话是否已过期（TTL）

3. **文档检索失败**
   ```
   Error: No relevant documents found
   ```
   - 确认相关文档已建立索引
   - 检查过滤条件（course_id, material_id）

### 调试命令

```bash
# 检查 Redis 连接
redis-cli ping

# 测试对话健康检查
curl http://localhost:8000/api/v1/conversation/health

# 获取配置信息
curl http://localhost:8000/api/v1/conversation/config

# 查看会话状态
curl http://localhost:8000/api/v1/conversation/conversations/{id}/status
```

---

## 📊 性能指标

| 操作     | 平均响应时间 | 吞吐量            | 备注           |
| -------- | ------------ | ----------------- | -------------- |
| 智能问答 | 2-8 秒       | 100 queries/min   | 取决于检索范围 |
| 会话管理 | <1 秒        | 1000 requests/min | Redis 缓存优化 |
| 配置查询 | <0.5 秒      | 2000 requests/min | 内存缓存       |

## 🔒 安全考虑

- 会话隔离：基于 conversation_id 的数据隔离
- 访问控制：基于课程和材料 ID 的内容过滤
- 会话管理：Redis TTL 自动清理过期会话
- 数据安全：敏感信息不记录在对话历史中

---

**🎯 智能对话系统为您提供强大的文档问答和对话管理能力！**