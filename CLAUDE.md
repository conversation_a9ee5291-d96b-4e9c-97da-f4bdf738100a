# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup
- **Activate virtual environment**: `.venv\Scripts\activate` (Windows) or `source .venv/bin/activate` (Linux/Mac)
- **Install dependencies**: `pip install -r requirements.txt`

### Server Management
- **Start development server**: `python -m app.main` or `uvicorn app.main:app --reload --host 0.0.0.0 --port 8000`
- **Docker development**: `docker-compose up` (runs Qdrant vector database)

**Note**: Always activate the virtual environment before running any Python commands.

### Testing
- **Run tests**: `pytest`
- **Run specific test**: `pytest tests/test_filename.py`
- **Run tests with coverage**: `pytest --cov=app tests/`
- **Test dependencies**: `pytest-asyncio`, `pytest-mock`, `httpx`

### Dependencies
- **Install dependencies**: `pip install -r requirements.txt` (after activating virtual environment)
- **Install with development extras**: `pip install -e ".[dev]"`
- **Install RAG dependencies**: `pip install -e ".[rag]"`

### Scripts
- **Build RAG index**: `python scripts/build_rag_index.py <directory> --course-id <id>`
- **Manage RAG data**: `python scripts/rag_data_manager.py`

## High-Level Architecture

### Core Components
This is a FastAPI-based AI backend service providing document processing and RAG (Retrieval-Augmented Generation) capabilities for educational content.

**Technology Stack:**
- **Web Framework**: FastAPI 0.116.1 with async/await support
- **AI Models**: OpenAI GPT-4o-mini for text generation and embeddings
- **Vector Database**: Qdrant for similarity search and retrieval
- **Cache/Memory**: Redis for conversation state management
- **Document Processing**: LlamaIndex 0.13.0 for text chunking and indexing

### Service Architecture

The application follows a layered architecture:

1. **API Layer** (`app/api/v1/`): REST endpoints organized by domain
   - `outline.py`: Document outline generation
   - `rag.py`: Vector indexing and retrieval
   - `conversation.py`: Chat interface with memory
   - `course_materials.py`: Unified document processing
   - `course.py`: Course-level operations

2. **Service Layer** (`app/services/`): Business logic implementation
   - `outline/`: Document analysis and outline generation
   - `rag/`: Vector indexing, similarity search, conversation engine
   - `course_material/`: File processing and cleanup

3. **Core Layer** (`app/core/`): Configuration and infrastructure
   - `config.py`: Environment-based settings with validation
   - `logging.py`: Structured logging with loguru
   - `deps.py`: Dependency injection for FastAPI

4. **Data Management**: 
   - `data/uploads/`: User-uploaded documents
   - `data/outputs/`: Generated outlines and processed content
   - `data/tmp/`: Temporary file storage

### Key Integrations

**RAG Configuration Management**: 
- Global LlamaIndex settings managed through `services/rag/rag_settings.py`
- Supports environment variable overrides with `RAG_` prefix
- Centralized configuration for Redis, Qdrant, and model parameters

**Document Processing Pipeline**:
1. File upload validation and storage
2. Content extraction and text chunking (512 chars, 50 overlap)
3. Vector embedding generation (text-embedding-3-small)
4. Qdrant collection indexing
5. Conversation memory setup in Redis

### Configuration

**Environment Variables** (defined in `app/core/config.py`):
- `API_KEY`: OpenAI API key (required)
- `BASE_URL`: OpenAI API base URL (default: https://api.openai.com/v1)
- `REDIS_URL`: Redis connection URL (default: redis://localhost:6379)
- `QDRANT_HOST/QDRANT_PORT`: Qdrant database connection
- `RAG_*`: RAG-specific overrides (chunk size, models, etc.)

**File Processing**:
- Supports `.md` and `.txt` files
- Maximum file size: 10MB
- UTF-8 encoding assumed

### Development Patterns

**Error Handling**: Global exception handlers with structured error responses
**Async/Await**: All services use async patterns for I/O operations
**Dependency Injection**: FastAPI dependencies for config and service instances
**Logging**: Structured JSON logging with request/response timing
**Validation**: Pydantic models for all request/response schemas

### External Services

**Required Services**:
- **Qdrant**: Vector database (localhost:6333/6334)
- **Redis**: Conversation memory (localhost:6379)
- **OpenAI API**: Language models and embeddings

The frontend is served independently (separate from this backend) and communicates via the REST API.