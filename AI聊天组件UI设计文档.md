# AI 聊天组件 UI 设计文档

## 整体布局

### 容器设计

- **位置**：页面右侧固定位置
- **尺寸**：宽度 45%，最小 320px，高度 100vh
- **外观**：圆角卡片设计，带有阴影效果
- **背景**：纯白色卡片背景
- **边框**：浅灰色细边框

### 响应式设计

- 移动端：全屏显示，去除侧边距
- 桌面端：右侧固定宽度显示

## 配色方案

### 主色调

- **主色**：蓝色系 (oklch(0.5500 0.1800 230.0000))
- **背景色**：浅灰白色 (oklch(0.9900 0.0020 106.0000))
- **卡片背景**：纯白色
- **文字主色**：深灰色 (oklch(0.2000 0.0050 264.0000))

### 功能色彩

- **危险操作**：橙红色 (oklch(0.6200 0.2000 25.0000))
- **次要信息**：中灰色 (oklch(0.5000 0.0100 264.0000))
- **边框色**：浅灰色 (oklch(0.9200 0.0100 220.0000))
- **输入框背景**：极浅灰色 (oklch(0.9800 0.0050 220.0000))

## 组件结构

### 1. 调试区域（顶部）

**功能**：可折叠的调试面板

- **标题栏**：
  - 左侧：设置图标 + "调试模式"文字
  - 右侧：下拉箭头图标
  - 背景：浅灰色，悬停时变为强调色
- **内容区**：
  - 两个并排输入框："Conservation ID" 和 "Course ID"
  - 输入框样式：圆角、浅色背景、聚焦时蓝色边框
- **交互**：点击标题栏可折叠/展开，带有平滑动画

### 2. 聊天记录区域（中部）

**功能**：显示对话历史

- **头部**：
  - 右侧：清除对话按钮（垃圾桶图标 + "清除对话"文字）
  - 按钮样式：透明背景、边框、悬停时变红色
- **消息列表**：
  - 滚动区域，自定义滚动条（4px 宽度，圆角）
  - 消息间距：16px

#### 消息样式

**AI 消息**：

- 头像：圆形，蓝色背景，白色"AI"文字
- 内容：浅灰色背景，圆角气泡
- 位置：左对齐
- 时间：左对齐，小字体

**用户消息**：

- 头像：圆形，浅色背景，"用"字
- 内容：蓝色背景，白色文字，圆角气泡
- 位置：右对齐
- 时间：右对齐，小字体

**打字指示器**：

- 三个跳动的小圆点
- 动画：依次放大缩小，循环播放

### 3. 输入区域（底部）

**功能**：消息输入和发送

#### 输入框

- **样式**：多行文本框，自动调整高度
- **最大高度**：10 行文本
- **占位符**："输入您的问题..."
- **边框**：聚焦时蓝色高亮
- **滚动条**：超出最大高度时显示

#### 控制栏

- **左侧**：模式选择下拉框
  - 选项："根据课件" / "通用知识"
  - 样式：小尺寸，圆角边框
- **右侧**：发送按钮
  - 图标：发送箭头
  - 样式：蓝色背景，白色文字和图标
  - 悬停效果：轻微放大 + 阴影

## 交互动效

### 页面加载

- 整个容器从下方淡入上升

### 消息动画

- AI 消息：从左侧滑入
- 用户消息：从右侧滑入
- 消息悬停：轻微上移

### 按钮交互

- 发送按钮：悬停放大，点击缩小
- 清除按钮：悬停变红，垃圾桶图标摇摆
- 调试折叠：箭头旋转 180 度

### 确认弹窗

- 背景：半透明黑色遮罩
- 弹窗：从上方滑入，白色卡片
- 按钮：取消（灰色）、确认（红色）

## 字体规范

### 字体族

- **主字体**：系统字体栈（苹方、微软雅黑等）
- **等宽字体**：JetBrains Mono（用于代码）

### 字体大小

- **标题**：16px（弹窗标题）
- **正文**：14px（消息内容）
- **小字**：12px（调试标签、按钮文字）
- **微字**：11px（时间戳）

## 间距规范

### 内边距

- **容器内边距**：16px
- **消息内边距**：8px 12px
- **按钮内边距**：6px 12px（小按钮）、8px 16px（主按钮）

### 外边距

- **消息间距**：16px
- **组件间距**：12px
- **元素间距**：6-8px

## 圆角规范

- **主圆角**：8px（容器、弹窗）
- **小圆角**：4px（按钮、输入框）
- **头像圆角**：50%（完全圆形）

## 阴影效果

- **容器阴影**：大阴影，营造悬浮感
- **按钮悬停阴影**：中等阴影
- **弹窗阴影**：超大阴影，突出层级

## 设计特点

### 现代化设计

- 采用卡片式布局，层次分明
- 圆角设计，视觉柔和
- 适度的阴影效果，增强立体感

### 用户体验

- 清晰的视觉层级
- 直观的交互反馈
- 流畅的动画过渡
- 响应式适配

### 色彩心理

- 蓝色主色调：专业、可信赖
- 浅色背景：简洁、清爽
- 红色警告：明确的危险提示

## 技术实现建议

### CSS 变量系统

- 使用 CSS 自定义属性管理颜色
- 便于主题切换和维护
- 支持 OKLCH 颜色空间

### 动画性能

- 使用 transform 和 opacity 进行动画
- 避免引起重排重绘的属性
- 合理使用 GPU 加速

### 无障碍设计

- 保证足够的颜色对比度
- 支持键盘导航
- 提供适当的焦点指示

这个设计采用了现代化的卡片式布局，配色清新简洁，交互流畅自然，适合作为 AI 聊天功能的用户界面。整体风格偏向简约专业，符合现代 Web 应用的设计趋势。

## 业务逻辑与 API 集成

### 核心业务流程

#### 1. 会话管理

**初始化流程**：

- 组件加载时生成唯一的 `conversation_id`（建议使用 UUID 或时间戳）
- 调用 `/api/v1/conversation/config` 获取系统配置信息
- 根据配置信息初始化聊天引擎参数

**会话状态监控**：

- 定期调用 `/api/v1/conversation/conversations/{id}/status` 检查会话状态
- 监控会话 TTL，在即将过期时提醒用户
- 处理会话过期情况，自动重新初始化

#### 2. 智能对话流程

**用户发送消息**：

1. 用户在输入框输入问题并点击发送
2. 前端验证输入内容（非空、长度限制）
3. 显示用户消息气泡和 AI 打字指示器
4. 构建 API 请求参数并调用对话接口

**API 调用逻辑**：

```javascript
// 发送消息到AI
async function sendMessage(question) {
  const requestData = {
    conversation_id: currentConversationId,
    question: question,
    chat_engine_type: selectedMode, // "condense_plus_context" 或 "simple"
    course_id: debugCourseId || undefined,
    course_material_id: debugMaterialId || undefined,
  };

  try {
    const response = await fetch("/api/v1/conversation/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestData),
    });

    const result = await response.json();
    return result;
  } catch (error) {
    handleApiError(error);
  }
}
```

**响应处理**：

1. 隐藏打字指示器
2. 显示 AI 回复消息气泡
3. 更新消息历史记录
4. 处理来源信息（如果有）
5. 滚动到最新消息

#### 3. 模式切换逻辑

**根据课件模式** (`condense_plus_context`)：

- 启用文档检索功能
- 使用调试区域的 Course ID 进行内容过滤
- 显示消息来源信息
- 适用于专业知识问答

**通用知识模式** (`simple`)：

- 直接对话，不检索文档
- 忽略课程过滤参数
- 适用于一般聊天和创意讨论

### API 接口集成详解

#### 1. 智能问答接口

**端点**：`POST /api/v1/conversation/chat`

**请求参数映射**：

- `conversation_id`：组件内部维护的会话 ID
- `question`：用户输入的问题文本
- `chat_engine_type`：模式选择器的值
- `course_id`：调试区域的 Course ID 输入框值
- `course_material_id`：调试区域的 Conservation ID 输入框值（注：文档中可能有拼写错误）

**响应数据处理**：

```javascript
function handleChatResponse(response) {
  // 显示AI回复
  addMessage("ai", response.answer);

  // 处理来源信息（仅在根据课件模式下显示）
  if (response.sources && response.sources.length > 0) {
    displaySourceInfo(response.sources);
  }

  // 更新性能指标（可选，用于调试）
  updatePerformanceMetrics({
    processingTime: response.processing_time,
    tokenUsage: response.token_usage,
  });
}
```

#### 2. 清除对话接口

**端点**：`DELETE /api/v1/conversation/conversations/{id}`

**触发时机**：

- 用户点击"清除对话"按钮
- 显示确认弹窗，用户确认后执行

**实现逻辑**：

```javascript
async function clearConversation() {
  try {
    const response = await fetch(
      `/api/v1/conversation/conversations/${currentConversationId}`,
      {
        method: "DELETE",
      }
    );

    if (response.ok) {
      // 清空本地消息历史
      clearMessageHistory();
      // 生成新的会话ID
      currentConversationId = generateNewConversationId();
      // 显示成功提示
      showSuccessMessage("对话已清除");
    }
  } catch (error) {
    showErrorMessage("清除对话失败");
  }
}
```

#### 3. 会话状态监控

**端点**：`GET /api/v1/conversation/conversations/{id}/status`

**监控策略**：

- 每 5 分钟检查一次会话状态
- 在用户发送消息前检查会话有效性
- TTL 剩余时间少于 5 分钟时显示警告

#### 4. 配置信息获取

**端点**：`GET /api/v1/conversation/config`

**用途**：

- 初始化时获取系统配置
- 动态调整 UI 参数（如 Token 限制提示）
- 验证可用的聊天引擎类型

### 错误处理机制

#### 网络错误处理

```javascript
function handleApiError(error) {
  // 网络连接错误
  if (error.name === "NetworkError") {
    showErrorMessage("网络连接失败，请检查网络设置");
    return;
  }

  // 服务器错误
  if (error.status >= 500) {
    showErrorMessage("服务器暂时不可用，请稍后重试");
    return;
  }

  // 客户端错误
  if (error.status >= 400) {
    showErrorMessage("请求参数错误，请检查输入内容");
    return;
  }
}
```

#### 业务逻辑错误

- **会话过期**：自动重新初始化会话
- **文档检索失败**：提示用户切换到通用知识模式
- **Token 限制超出**：提示用户简化问题或清除历史

### 性能优化策略

#### 1. 请求优化

- 实现请求防抖，避免重复发送
- 使用 AbortController 支持请求取消
- 长时间无响应时显示超时提示

#### 2. 数据缓存

- 缓存系统配置信息，减少重复请求
- 本地存储会话历史，页面刷新后恢复
- 缓存常用的 API 响应结果

#### 3. UI 性能

- 虚拟滚动处理大量消息历史
- 懒加载消息内容和来源信息
- 优化动画性能，使用 CSS transform

### 数据流图

```
用户输入 → 前端验证 → API调用 → 后端处理 → 响应返回 → UI更新
    ↓           ↓          ↓         ↓         ↓        ↓
  输入框    参数构建    HTTP请求   RAG处理   JSON响应  消息显示
    ↓           ↓          ↓         ↓         ↓        ↓
  发送按钮   错误处理    网络传输   LLM生成   数据解析  滚动定位
```

### 状态管理

#### 组件状态

```javascript
const chatState = {
  // 会话管理
  conversationId: null,
  isConnected: true,
  sessionStatus: "active",

  // 消息管理
  messages: [],
  isTyping: false,

  // 配置管理
  chatMode: "condense_plus_context", // 或 'simple'
  debugConfig: {
    courseId: "",
    materialId: "",
  },

  // 系统配置
  systemConfig: null,
  availableEngines: [],
};
```

#### 状态更新流程

1. **初始化**：加载配置 → 生成会话 ID → 设置默认状态
2. **发送消息**：更新消息列表 → 设置打字状态 → 调用 API
3. **接收回复**：清除打字状态 → 添加 AI 消息 → 更新 UI
4. **清除会话**：重置消息列表 → 生成新会话 ID → 刷新状态

这个业务逻辑设计确保了 AI 聊天组件能够完整地集成智能对话系统的所有功能，提供流畅的用户体验和可靠的错误处理机制。
